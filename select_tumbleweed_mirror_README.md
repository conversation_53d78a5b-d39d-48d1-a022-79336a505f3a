# select_tumbleweed_mirror.py - openSUSE Tumbleweed 镜像源自动配置脚本

## 功能描述

这是一个用 Python3 编写的 openSUSE Tumbleweed 镜像源自动配置脚本，能够：

- 从 openSUSE 官方获取镜像站点列表
- 自动检测亚洲和北美区域的镜像站点
- 使用 asyncio 并行测试镜像延迟和可用性
- 在5秒内选择延迟最低的20个可用镜像
- 自动配置 zypper 使用最优镜像

## 特性

### ✅ 无外部依赖
- 仅使用 Python3 标准库
- 不需要安装额外的 pip 包
- 兼容 Python 3.7+

### ✅ 智能镜像发现
- 从官方镜像列表获取最新站点信息
- 自动过滤亚洲和北美区域镜像
- 内置17个高质量备用镜像站点

### ✅ 高性能并行测试
- 使用 asyncio 并行测试所有镜像
- 5秒内完成所有测试
- 同时检查 ping 延迟和仓库可用性

### ✅ 彻底配置清理
- 完全清理已有的 OSS/Non-OSS 配置
- 删除相关配置文件避免冲突
- 支持测试模式预览操作

## 支持的镜像区域

### 亚洲镜像站
- **中国**: 清华TUNA、中科大、上海交大、南京大学、兰州大学、北京外国语大学
- **日本**: RIKEN、OSDN、JAIST
- **韩国**: Kakao、KAIST

### 北美镜像站
- **美国**: Kernel.org、FCIX、UC Berkeley、Princeton、MIT
- **加拿大**: University of Waterloo、MUUG

## 使用方法

### 基本使用
```bash
# 测试模式（推荐先运行）
TEST_MODE=1 python3 select_tumbleweed_mirror.py

# 正常使用（需要 sudo 权限）
sudo python3 select_tumbleweed_mirror.py

# 或者直接执行
sudo ./select_tumbleweed_mirror.py
```

### 环境变量
- `TEST_MODE=1`: 启用测试模式，不实际修改系统配置

## 工作流程

1. **显示当前配置**: 显示当前的 zypper 仓库配置
2. **获取镜像列表**: 
   - 尝试从官方镜像列表获取最新站点
   - 失败时使用内置的高质量镜像列表
   - 自动过滤亚洲和北美区域镜像
3. **并行性能测试**: 
   - 使用 asyncio 并行测试所有镜像
   - 同时进行 ping 延迟测试和仓库可用性检查
   - 5秒内完成所有测试
4. **智能选择**: 选择延迟最低的20个可用镜像
5. **配置清理**: 彻底清理已有配置
6. **自动配置**: 按延迟顺序尝试配置镜像
7. **验证结果**: 显示最终配置状态

## 性能特点

- **快速**: 5秒内完成所有镜像测试
- **并行**: 同时测试多个镜像，充分利用网络带宽
- **智能**: 自动选择最优镜像，无需手动干预
- **可靠**: 多重备用方案，确保总能找到可用镜像

## 配置的仓库

脚本会配置以下仓库：
- `repo-oss`: OSS (Open Source Software) 仓库
- `repo-non-oss`: Non-OSS 仓库

注意：Tumbleweed 是滚动发布版本，不需要单独的 update 仓库。

## 依赖工具

- `python3`: Python 3.7+ 解释器
- `ping`: 用于测试网络延迟
- `curl`: 用于检查镜像站可用性
- `zypper`: openSUSE 包管理器
- `sudo`: 用于获取管理员权限（非测试模式）

## 故障排除

### 常见问题

1. **无法获取镜像列表**
   - 脚本会自动使用内置镜像列表
   - 检查网络连接是否正常

2. **所有镜像都不可用**
   - 脚本会自动回退到官方源
   - 检查防火墙设置

3. **配置失败**
   - 确保有 sudo 权限
   - 检查 zypper 是否正常工作

### 调试模式

使用测试模式查看脚本的选择逻辑：
```bash
TEST_MODE=1 python3 select_tumbleweed_mirror.py
```

## 与 Bash 版本的对比

| 特性 | Bash 版本 | Python 版本 |
|------|-----------|-------------|
| 维护性 | 较难维护 | 易于维护 |
| 镜像列表 | 硬编码 | 动态获取 |
| 并行测试 | 有限支持 | 完全并行 |
| 测试速度 | ~20秒 | ~5秒 |
| 错误处理 | 基础 | 完善 |
| 代码可读性 | 一般 | 优秀 |

## 许可证

本脚本遵循与 openSUSE 项目相同的开源许可证。

#!/bin/bash

# 镜像站列表
MIRRORS=(
  # 中国镜像站
  "https://mirrors.tuna.tsinghua.edu.cn/opensuse/tumbleweed/repo/"
  "https://mirrors.ustc.edu.cn/opensuse/tumbleweed/repo/"
  "https://ftp.sjtu.edu.cn/opensuse/tumbleweed/repo/"
  "https://mirrors.nju.edu.cn/opensuse/tumbleweed/repo/"
  # 日本镜像站
  "https://ftp.riken.jp/Linux/opensuse/tumbleweed/repo/"
  "https://mirrors.dl.osdn.jp/opensuse-tumbleweed/repo/"
  # 美国镜像站
  "https://mirrors.kernel.org/opensuse/tumbleweed/repo/"
  "https://mirror.fcix.net/opensuse/tumbleweed/repo/"
  "https://mirrors.ocf.berkeley.edu/opensuse/tumbleweed/repo/"
)

# 官方源
OFFICIAL_BASE="https://download.opensuse.org/tumbleweed/repo/"

# 临时文件用于存储 ping 延迟
TEMP_FILE=$(mktemp)

# 清理临时文件
cleanup() {
  rm -f "$TEMP_FILE"
}
trap cleanup EXIT

# 检查镜像是否有效（即镜像同步是否完成）
check_mirror() {
  local URL=$1
  echo "  检查 OSS 仓库..."

  # 检查 oss/repodata/repomd.xml 是否存在，设置2秒超时
  if ! timeout 2s curl -s --connect-timeout 2 --max-time 2 --head "${URL}oss/repodata/repomd.xml" | head -n 1 | grep -q "200 OK"; then
    echo "  OSS 仓库不可用"
    return 1
  fi

  echo "  检查 Non-OSS 仓库..."
  # 检查 non-oss/repodata/repomd.xml 是否存在，设置2秒超时
  if ! timeout 2s curl -s --connect-timeout 2 --max-time 2 --head "${URL}non-oss/repodata/repomd.xml" | head -n 1 | grep -q "200 OK"; then
    echo "  Non-OSS 仓库不可用"
    return 1
  fi

  # 对于 Tumbleweed，update 仓库可能不存在，这是正常的
  # 只检查 OSS 和 Non-OSS 仓库即可
  echo "  镜像检查通过"
  return 0
}

# 获取镜像站的 ping 延迟（毫秒），并设置超时为2秒
get_ping_latency() {
  local URL=$1
  local HOST=$(echo "$URL" | sed 's|https://||;s|/.*||')  # 提取域名

  # 使用更可靠的ping命令，兼容不同系统，设置2秒超时
  local LATENCY
  if command -v ping >/dev/null 2>&1; then
    # Linux系统，使用timeout命令确保2秒内完成
    LATENCY=$(timeout 2s ping -c 2 -W 1 "$HOST" 2>/dev/null | grep 'rtt\|round-trip' | awk -F '/' '{print $(NF-1)}' | head -1)
    if [[ -z "$LATENCY" ]]; then
      # 尝试另一种格式
      LATENCY=$(timeout 2s ping -c 2 -W 1 "$HOST" 2>/dev/null | tail -1 | awk -F '/' '{print $5}')
    fi
  fi

  if [[ -z "$LATENCY" ]] || [[ ! "$LATENCY" =~ ^[0-9]+\.?[0-9]*$ ]]; then
    LATENCY=9999  # 如果 ping 失败，返回一个较高的延迟值
  fi

  echo "$LATENCY $URL"
}

# 显示当前 zypper 仓库配置
show_current_repos() {
  echo "当前 zypper 仓库配置："
  if command -v zypper >/dev/null 2>&1; then
    zypper repos --uri 2>/dev/null | grep -E "(Enabled|repo-oss|repo-non-oss|repo-update|oss|non-oss|tumbleweed)" || echo "  没有找到相关仓库"
  else
    echo "  zypper 命令不可用"
  fi
  echo ""
}

# 导出函数以便在子shell中使用
export -f get_ping_latency

# 更新源配置
update_zypper_repo() {
  local BASE_URL=$1
  echo "镜像有效，正在更新 zypper 配置..."

  # 检查是否为测试模式
  if [[ "${TEST_MODE:-}" == "1" ]]; then
    echo "[测试模式] 将移除现有的仓库..."
    echo "[测试模式] 将清理残留的配置文件..."
    echo "[测试模式] 将删除其他 OSS/Non-OSS 相关配置文件..."
    echo "[测试模式] 将添加 OSS 仓库: ${BASE_URL}oss/"
    echo "[测试模式] 将添加 Non-OSS 仓库: ${BASE_URL}non-oss/"
    echo "[测试模式] 将刷新源..."
    echo "[测试模式] zypper 配置完成！"
    return 0
  fi

  # 移除现有的仓库和配置文件
  echo "移除现有仓库..."

  # 使用 zypper 命令移除仓库
  sudo zypper rr repo-oss 2>/dev/null || true
  sudo zypper rr repo-non-oss 2>/dev/null || true
  sudo zypper rr repo-update 2>/dev/null || true

  # 删除可能残留的配置文件
  echo "清理残留的配置文件..."
  sudo rm -f /etc/zypp/repos.d/repo-oss.repo 2>/dev/null || true
  sudo rm -f /etc/zypp/repos.d/repo-non-oss.repo 2>/dev/null || true
  sudo rm -f /etc/zypp/repos.d/repo-update.repo 2>/dev/null || true

  # 删除其他可能的 OSS/Non-OSS 相关配置文件
  echo "删除其他 OSS/Non-OSS 相关配置文件..."
  sudo find /etc/zypp/repos.d/ -name "*oss*.repo" -delete 2>/dev/null || true
  sudo find /etc/zypp/repos.d/ -name "*non-oss*.repo" -delete 2>/dev/null || true
  sudo find /etc/zypp/repos.d/ -name "*tumbleweed*.repo" -delete 2>/dev/null || true

  # 添加有效的 OSS 和 Non-OSS 镜像
  echo "添加 OSS 仓库: ${BASE_URL}oss/"
  if ! sudo zypper ar -cfp 90 "${BASE_URL}oss/" repo-oss; then
    echo "添加 OSS 仓库失败"
    return 1
  fi

  echo "添加 Non-OSS 仓库: ${BASE_URL}non-oss/"
  if ! sudo zypper ar -cfp 90 "${BASE_URL}non-oss/" repo-non-oss; then
    echo "添加 Non-OSS 仓库失败"
    return 1
  fi

  echo "配置更新完毕，正在刷新源..."
  if ! sudo zypper ref; then
    echo "刷新源失败，可能镜像同步不完整"
    return 1
  fi

  echo "zypper 配置完成！"
  echo ""

  # 显示最终配置
  echo "=== 最终 zypper 仓库配置 ==="
  show_current_repos
}

echo "=== openSUSE Tumbleweed 镜像源自动配置脚本 ==="
echo ""

# 显示当前配置
show_current_repos

echo "开始测试镜像延迟..."

# 测试所有镜像的延迟并排序
echo "正在测试镜像延迟..."
> "$TEMP_FILE"  # 清空临时文件
for mirror in "${MIRRORS[@]}"; do
  echo "  测试镜像: $mirror"
  result=$(get_ping_latency "$mirror")
  echo "$result" >> "$TEMP_FILE"
done

# 按延迟排序
mapfile -t sorted_mirrors < <(sort -n "$TEMP_FILE")

echo "延迟测试完成，按延迟排序的镜像："
for mirror_info in "${sorted_mirrors[@]}"; do
  latency=$(echo "$mirror_info" | awk '{print $1}')
  url=$(echo "$mirror_info" | awk '{print $2}')
  echo "  延迟: ${latency}ms - $url"
done

# 遍历镜像站列表并尝试更新（按延迟排序）
echo "开始检查镜像可用性..."
for mirror_info in "${sorted_mirrors[@]}"; do
  mirror_url=$(echo "$mirror_info" | awk '{print $2}')
  echo "正在检查镜像：$mirror_url"
  if check_mirror "$mirror_url"; then
    echo "镜像 $mirror_url 可用，正在配置..."
    if update_zypper_repo "$mirror_url"; then
      echo "配置完成！"
      exit 0
    else
      echo "配置失败，尝试下一个镜像..."
    fi
  else
    echo "镜像 $mirror_url 不可用，尝试下一个..."
  fi
done

# 如果没有镜像可用，回退到官方源
echo "没有有效的镜像，正在使用官方源..."
echo "配置官方源: $OFFICIAL_BASE"
update_zypper_repo "$OFFICIAL_BASE"
echo "已配置官方源。"

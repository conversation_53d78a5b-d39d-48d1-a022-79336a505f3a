#!/usr/bin/env python3
"""
openSUSE Tumbleweed 镜像源自动配置脚本
从官方获取镜像列表，自动选择最优镜像并配置 zypper
"""

import asyncio
import os
import subprocess
import sys
import time
from urllib.parse import urljoin, urlparse
from urllib.request import urlopen, Request


class TumbleweedMirrorSelector:
    def __init__(self):
        # 使用多个镜像列表源
        self.mirror_list_urls = [
            "https://download.opensuse.org/tumbleweed/repo/oss/repodata/repomd.xml.mirrorlist",
            "https://mirrors.opensuse.org/mirrorlist?project=openSUSE%3ATumbleweed&repo=oss",
        ]
        self.test_mode = os.environ.get('TEST_MODE', '0') == '1'
        self.timeout = 2  # 2秒超时
        self.max_mirrors = 20  # 最多选择20个镜像
        self.total_timeout = 5  # 总测试时间5秒
        
    def log(self, message):
        """打印日志信息"""
        print(f"[{time.strftime('%H:%M:%S')}] {message}")
        
    def get_mirror_list_from_official(self):
        """从官方获取镜像列表"""
        mirrors = []

        for url in self.mirror_list_urls:
            try:
                self.log(f"从 {url} 获取镜像列表...")
                req = Request(url, headers={'User-Agent': 'Mozilla/5.0'})
                with urlopen(req, timeout=10) as response:
                    content = response.read().decode('utf-8')

                # 解析镜像列表
                for line in content.split('\n'):
                    line = line.strip()
                    if line.startswith('http'):
                        # 处理不同格式的镜像列表
                        if 'tumbleweed/repo/oss' in line:
                            # 提取基础URL（去掉 /oss 部分）
                            base_url = line.replace('/oss/', '/').replace('/oss', '/').rstrip('/')
                            if not base_url.endswith('/'):
                                base_url += '/'
                            mirrors.append(base_url)
                        elif 'opensuse/tumbleweed' in line:
                            # 处理其他格式的URL
                            if line.endswith('/'):
                                mirrors.append(line)
                            else:
                                mirrors.append(line + '/')

                if mirrors:
                    self.log(f"从 {url} 获取到 {len(mirrors)} 个镜像")
                    break  # 成功获取就退出

            except Exception as e:
                self.log(f"从 {url} 获取镜像列表失败: {e}")
                continue

        return list(set(mirrors))  # 去重
        
    def get_builtin_mirrors(self):
        """获取内置的亚洲和北美镜像列表"""
        return [
            # 中国镜像站
            "https://mirrors.tuna.tsinghua.edu.cn/opensuse/tumbleweed/repo/",
            "https://mirrors.ustc.edu.cn/opensuse/tumbleweed/repo/",
            "https://ftp.sjtu.edu.cn/opensuse/tumbleweed/repo/",
            "https://mirrors.nju.edu.cn/opensuse/tumbleweed/repo/",
            "https://mirror.lzu.edu.cn/opensuse/tumbleweed/repo/",
            "https://mirrors.bfsu.edu.cn/opensuse/tumbleweed/repo/",
            # 日本镜像站
            "https://ftp.riken.jp/Linux/opensuse/tumbleweed/repo/",
            "https://mirrors.dl.osdn.jp/opensuse-tumbleweed/repo/",
            "https://ftp.jaist.ac.jp/pub/Linux/openSUSE/tumbleweed/repo/",
            # 韩国镜像站
            "https://mirror.kakao.com/opensuse/tumbleweed/repo/",
            "https://ftp.kaist.ac.kr/opensuse/tumbleweed/repo/",
            # 美国镜像站
            "https://mirrors.kernel.org/opensuse/tumbleweed/repo/",
            "https://mirror.fcix.net/opensuse/tumbleweed/repo/",
            "https://mirrors.ocf.berkeley.edu/opensuse/tumbleweed/repo/",
            "https://mirror.math.princeton.edu/pub/opensuse/tumbleweed/repo/",
            "https://mirrors.mit.edu/opensuse/tumbleweed/repo/",
            # 加拿大镜像站
            "https://mirror.csclub.uwaterloo.ca/opensuse/tumbleweed/repo/",
            "https://muug.ca/mirror/opensuse/tumbleweed/repo/",
        ]

    def get_mirror_list(self):
        """获取镜像列表"""
        mirrors = self.get_mirror_list_from_official()

        if not mirrors:
            self.log("无法从官方获取镜像列表，使用内置镜像列表...")
            mirrors = self.get_builtin_mirrors()

        # 过滤亚洲和北美镜像（基于域名判断）
        asia_na_mirrors = []
        for mirror in mirrors:
            hostname = urlparse(mirror).hostname.lower()
            # 亚洲域名特征
            if any(region in hostname for region in ['.cn', '.jp', '.kr', '.tw', '.hk', '.sg', '.in']):
                asia_na_mirrors.append(mirror)
            # 北美域名特征
            elif any(region in hostname for region in ['.com', '.edu', '.org', '.net']) and \
                 any(country in hostname for country in ['mit', 'berkeley', 'princeton', 'kernel', 'fcix', 'uwaterloo', 'muug']):
                asia_na_mirrors.append(mirror)

        if asia_na_mirrors:
            mirrors = asia_na_mirrors

        self.log(f"获取到 {len(mirrors)} 个亚洲和北美镜像站点")
        return mirrors
        
    async def ping_host(self, url):
        """异步ping测试"""
        try:
            parsed = urlparse(url)
            hostname = parsed.hostname
            
            # 使用 ping 命令测试延迟
            process = await asyncio.create_subprocess_exec(
                'ping', '-c', '2', '-W', '1', hostname,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=self.timeout)
                
                if process.returncode == 0:
                    # 解析ping结果
                    output = stdout.decode('utf-8')
                    # 查找平均延迟
                    import re
                    match = re.search(r'rtt min/avg/max/mdev = [\d.]+/([\d.]+)/', output)
                    if match:
                        latency = float(match.group(1))
                        return latency, url
                        
            except asyncio.TimeoutError:
                pass
                
        except Exception:
            pass
            
        return 9999, url  # 失败时返回高延迟
        
    async def check_mirror_availability(self, url):
        """异步检查镜像可用性"""
        try:
            # 检查 OSS 和 Non-OSS 仓库
            oss_url = urljoin(url, 'oss/repodata/repomd.xml')
            non_oss_url = urljoin(url, 'non-oss/repodata/repomd.xml')
            
            # 使用 curl 检查
            for check_url in [oss_url, non_oss_url]:
                process = await asyncio.create_subprocess_exec(
                    'curl', '-s', '--connect-timeout', '2', '--max-time', '2', 
                    '--head', check_url,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                try:
                    stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=self.timeout)
                    
                    if process.returncode != 0:
                        return False
                        
                    output = stdout.decode('utf-8')
                    if '200 OK' not in output:
                        return False
                        
                except asyncio.TimeoutError:
                    return False
                    
            return True
            
        except Exception:
            return False
            
    async def test_mirrors(self, mirrors):
        """并行测试镜像延迟和可用性"""
        self.log(f"开始并行测试 {len(mirrors)} 个镜像...")
        
        # 首先并行测试ping延迟
        ping_tasks = [self.ping_host(mirror) for mirror in mirrors]
        
        try:
            ping_results = await asyncio.wait_for(
                asyncio.gather(*ping_tasks, return_exceptions=True),
                timeout=self.total_timeout
            )
        except asyncio.TimeoutError:
            self.log("Ping测试超时，使用已完成的结果")
            ping_results = []
            for task in ping_tasks:
                if task.done():
                    try:
                        ping_results.append(task.result())
                    except:
                        ping_results.append((9999, ""))
                else:
                    task.cancel()
                    ping_results.append((9999, ""))
        
        # 过滤和排序结果
        valid_results = [(latency, url) for latency, url in ping_results 
                        if isinstance(latency, (int, float)) and latency < 9999]
        valid_results.sort(key=lambda x: x[0])
        
        # 选择延迟最低的镜像进行可用性检查
        top_mirrors = valid_results[:self.max_mirrors * 2]  # 多选一些以防部分不可用
        
        self.log(f"选择延迟最低的 {len(top_mirrors)} 个镜像进行可用性检查...")
        
        # 并行检查可用性
        availability_tasks = []
        for latency, url in top_mirrors:
            availability_tasks.append(self.check_availability_with_latency(url, latency))
            
        try:
            availability_results = await asyncio.wait_for(
                asyncio.gather(*availability_tasks, return_exceptions=True),
                timeout=self.total_timeout
            )
        except asyncio.TimeoutError:
            self.log("可用性检查超时，使用已完成的结果")
            availability_results = []
            for task in availability_tasks:
                if task.done():
                    try:
                        availability_results.append(task.result())
                    except:
                        availability_results.append(None)
                else:
                    task.cancel()
                    availability_results.append(None)
        
        # 过滤可用的镜像
        available_mirrors = [result for result in availability_results 
                           if result is not None]
        available_mirrors.sort(key=lambda x: x[0])  # 按延迟排序
        
        return available_mirrors[:self.max_mirrors]
        
    async def check_availability_with_latency(self, url, latency):
        """检查镜像可用性并返回延迟信息"""
        if await self.check_mirror_availability(url):
            return latency, url
        return None
        
    def show_current_repos(self):
        """显示当前zypper仓库配置"""
        self.log("当前 zypper 仓库配置：")
        try:
            result = subprocess.run(['zypper', 'lr'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                relevant_lines = [line for line in lines 
                                if any(keyword in line.lower() 
                                      for keyword in ['oss', 'non-oss', 'tumbleweed', 'repo-'])]
                if relevant_lines:
                    for line in relevant_lines:
                        print(f"  {line}")
                else:
                    print("  没有找到相关仓库")
            else:
                print("  无法获取仓库信息")
        except Exception as e:
            print(f"  获取仓库信息失败: {e}")
        print()
        
    def cleanup_existing_repos(self):
        """清理现有的仓库配置"""
        if self.test_mode:
            self.log("[测试模式] 将移除现有的仓库...")
            self.log("[测试模式] 将清理残留的配置文件...")
            self.log("[测试模式] 将删除其他 OSS/Non-OSS 相关配置文件...")
            return True
            
        self.log("移除现有仓库...")
        
        # 移除zypper仓库
        for repo in ['repo-oss', 'repo-non-oss', 'repo-update']:
            try:
                subprocess.run(['sudo', 'zypper', 'rr', repo], 
                             capture_output=True, timeout=30)
            except:
                pass
                
        # 删除配置文件
        self.log("清理残留的配置文件...")
        config_files = [
            '/etc/zypp/repos.d/repo-oss.repo',
            '/etc/zypp/repos.d/repo-non-oss.repo', 
            '/etc/zypp/repos.d/repo-update.repo'
        ]
        
        for config_file in config_files:
            try:
                subprocess.run(['sudo', 'rm', '-f', config_file], timeout=10)
            except:
                pass
                
        # 删除其他相关配置文件
        self.log("删除其他 OSS/Non-OSS 相关配置文件...")
        try:
            subprocess.run(['sudo', 'find', '/etc/zypp/repos.d/', '-name', '*oss*.repo', '-delete'], timeout=30)
            subprocess.run(['sudo', 'find', '/etc/zypp/repos.d/', '-name', '*non-oss*.repo', '-delete'], timeout=30)
            subprocess.run(['sudo', 'find', '/etc/zypp/repos.d/', '-name', '*tumbleweed*.repo', '-delete'], timeout=30)
        except:
            pass
            
        return True
        
    def configure_zypper(self, mirror_url):
        """配置zypper仓库"""
        self.log(f"配置镜像: {mirror_url}")

        if self.test_mode:
            self.log(f"[测试模式] 将添加 OSS 仓库: {mirror_url}oss/")
            self.log(f"[测试模式] 将添加 Non-OSS 仓库: {mirror_url}non-oss/")
            self.log("[测试模式] 将刷新源...")
            self.log("[测试模式] zypper 配置完成！")
            return True

        try:
            # 先清理可能存在的仓库
            self.cleanup_existing_repos()

            # 添加OSS仓库
            self.log(f"添加 OSS 仓库: {mirror_url}oss/")
            result = subprocess.run([
                'sudo', 'zypper', 'ar', '-cfp', '90',
                f'{mirror_url}oss/', 'repo-oss'
            ], capture_output=True, text=True, timeout=60)

            if result.returncode != 0:
                self.log(f"添加 OSS 仓库失败: {result.stderr.strip()}")
                self.cleanup_existing_repos()  # 清理失败的配置
                return False

            # 添加Non-OSS仓库
            self.log(f"添加 Non-OSS 仓库: {mirror_url}non-oss/")
            result = subprocess.run([
                'sudo', 'zypper', 'ar', '-cfp', '90',
                f'{mirror_url}non-oss/', 'repo-non-oss'
            ], capture_output=True, text=True, timeout=60)

            if result.returncode != 0:
                self.log(f"添加 Non-OSS 仓库失败: {result.stderr.strip()}")
                self.cleanup_existing_repos()  # 清理失败的配置
                return False

            # 刷新仓库
            self.log("配置更新完毕，正在刷新源...")
            result = subprocess.run(['sudo', 'zypper', 'ref'],
                                  capture_output=True, text=True, timeout=120)

            if result.returncode != 0:
                self.log(f"刷新源失败: {result.stderr.strip()}")
                self.cleanup_existing_repos()  # 清理失败的配置
                return False

            self.log("zypper 配置完成！")
            return True

        except Exception as e:
            self.log(f"配置失败: {e}")
            self.cleanup_existing_repos()  # 清理失败的配置
            return False
            
    async def run(self):
        """主运行函数"""
        self.log("=== openSUSE Tumbleweed 镜像源自动配置脚本 ===")
        print()
        
        # 显示当前配置
        self.show_current_repos()
        
        # 获取镜像列表
        mirrors = self.get_mirror_list()
        if not mirrors:
            self.log("无法获取镜像列表")
            return False
            
        # 测试镜像
        available_mirrors = await self.test_mirrors(mirrors)
        
        if not available_mirrors:
            self.log("没有找到可用的镜像")
            return False
            
        self.log("延迟测试完成，按延迟排序的镜像：")
        for latency, url in available_mirrors:
            self.log(f"  延迟: {latency:.1f}ms - {url}")
        print()
        
        # 清理现有配置
        if not self.cleanup_existing_repos():
            self.log("清理现有配置失败")
            return False
            
        # 尝试配置最优镜像
        for latency, mirror_url in available_mirrors:
            self.log(f"尝试配置镜像: {mirror_url}")
            if self.configure_zypper(mirror_url):
                self.log("配置完成！")
                print()
                self.log("=== 最终 zypper 仓库配置 ===")
                self.show_current_repos()
                return True
            else:
                self.log("配置失败，尝试下一个镜像...")
                
        # 如果所有镜像都失败，使用官方源
        self.log("所有镜像都不可用，使用官方源...")
        official_url = "https://download.opensuse.org/tumbleweed/repo/"
        if self.configure_zypper(official_url):
            self.log("官方源配置完成")
            return True
        else:
            self.log("官方源配置也失败了")
            return False


async def main():
    """主函数"""
    selector = TumbleweedMirrorSelector()
    success = await selector.run()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行出错: {e}")
        sys.exit(1)

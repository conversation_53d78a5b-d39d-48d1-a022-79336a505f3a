<!DOCTYPE html>
<html lang="en">
  <head>
      <!-- Meta, title, CSS, favicons, etc. -->
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
      <meta name="author" content="MirrorCache contributors">

      <meta name="csrf-token" content="6ae0249b6f87a46746b20fd0882783e4bcce6384" />
      <meta name="csrf-param" content="csrf_token" />


      <title>Mirrors for /tumbleweed/repo/oss/repodata/repomd.xml - openSUSE Download</title>

      <!-- Bootstrap core CSS -->
      <link href="/asset/d9561a918a/bootstrap.css" rel="stylesheet">
      <script src="/asset/ccdc75e705/bootstrap.js"></script>

      <!-- Chameleon Style -->
      <!-- <link rel="stylesheet" href="https://static.opensuse.org/chameleon-3.0/dist/css/chameleon.css" /> -->
      <link href="/asset/39ed1a6b79/chameleon.css" rel="stylesheet">
      <!-- Chameleon Script -->
      <script defer src="https://static.opensuse.org/chameleon-3.0/dist/js/chameleon.js"></script>

      
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.2/dist/leaflet.css" integrity="sha256-sA+zWATbFveLLNqWO2gtiw3HL/lh1giY/Inf1BJ0z14=" crossorigin=""/>
<script src="https://unpkg.com/leaflet@1.9.2/dist/leaflet.js" integrity="sha256-o9N1jGDZrf5tS+Ft4gbIK7mYMipq9lqpVJ91xHSyKhg=" crossorigin=""></script>
<style type='text/css'>
img.huechange  { filter: hue-rotate(120deg) }
img.huechange1 { filter: hue-rotate(90deg) }
</style>


<script>
var is_operator = false;
var preferred_url = "";
var lat = 31.2222;
var lng = 121.4581;
</script>



      <script>//<![CDATA[

          
          $(function() {
            setupForAll();
            
          } );

//]]></script>
      <link id="favicon-16" rel="icon" href="/asset/e207a92f20/logo-16.png" sizes="16x16" type="image/png">
      <link id="favicon-svg" rel="icon" href="/asset/f8027e92e2/logo.svg" sizes="any" type="image/svg+xml">

  </head>
  <body>
      
<nav class="navbar noprint navbar-expand-md sticky-top">
  <a class="navbar-brand" href="/"><img src="https://static.opensuse.org/favicon.svg" class="d-inline-block align-top" width="30" height="30" alt='openSUSE icon'> <span class="navbar-title">Download</span></a>
  <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-collapse"><svg width="20" height="20" viewbox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M2.5 11.5A.5.5 0 0 1 3 11h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4A.5.5 0 0 1 3 7h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4A.5.5 0 0 1 3 3h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"></path></svg></button>

  <div class="collapse navbar-collapse" id="navbar-collapse">

    <ul class="nav navbar-nav mr-auto flex-md-shrink-0">
      <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle" href="#" role="button" data-toggle="dropdown"
        aria-haspopup="true" aria-expanded="false">Shortcuts</a>
        <div class="dropdown-menu">
          <a class="dropdown-item" href="/debug/">debug</a> <a class="dropdown-item" href=
          "/distribution/">distribution</a> <a class="dropdown-item" href="/factory/">factory</a>
          <a class="dropdown-item" href="/ports/">ports</a> <a class="dropdown-item" href=
          "/repositories/">repositories</a> <a class="dropdown-item" href="/source/">source</a>
          <a class="dropdown-item" href="/tumbleweed/">tumbleweed</a> <a class="dropdown-item"
          href="/update/">update</a>
        </div>
      </li>
    </ul>

    <ul id="user-menu" class="navbar-nav">
      <li class="nav-item dropdown">
        <a class="nav-link" href="#" id="user-dropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
          <img src="/asset/f8027e92e2/logo.svg" alt="openSUSE logo">
          <span class="d-md-none">MirrorCache</span>
        </a>
        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="user-dropdown">
          <a class="dropdown-item" href="/app/server">Mirrors</a>
          <a class="dropdown-item" href="/app/package">Packages</a>
          <a class="dropdown-item" href="/app/project">Projects</a>
          <a class="dropdown-item" href="/app/efficiency">Efficiency</a>
          <a class="dropdown-item" href="/rest/stat">Statistics</a>
          <div class="dropdown-divider"></div>
          <h3 class="dropdown-header">User menu</h3>

          <a class="dropdown-item" href="/login">Log in</a>
        </div>
      </li>
      <li class="nav-item dropdown" id="reports">
      <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false" data-submenu>Reports<span class="caret"></span></a>
        <ul class="dropdown-menu">
          <li>
          <a class="dropdown-item" href="/report/mirrors">All Mirrors</a>
          </li><li>
          <a class="dropdown-item" href="/report/mirrors/154">15.4 Mirrors</a>
          </li><li>
          <a class="dropdown-item" href="/report/mirrors/155">15.5 Mirrors</a>
          </li><li>
          <a class="dropdown-item" href="/report/mirrors/tumbleweed">TW Mirrors</a>
          </li><li>
          <a class="dropdown-item" href="/report/mirrors/repositories">Build Service Mirrors</a>
          </li><li>
          <a class="dropdown-item" href="/report/download?group=project">Downloads/project</a>
          </li><li>
          <a class="dropdown-item" href="/report/download?group=mirror">Downloads/mirror</a>
          </li><li>
          <a class="dropdown-item" href="/report/download?group=os">Downloads/OS</a>
          </li><li>
          <a class="dropdown-item" href="/report/download?group=country">Downloads/country</a>
          </li><li>
          <a class="dropdown-item" href="/report/download?group=arch">Downloads/arch</a>
          </li><li>
          <a class="dropdown-item" href="/report/download?group=type">Downloads/type</a>
          </li>
        </ul>
      </li>
    </ul>

  </div>

  <button class="navbar-toggler megamenu-toggler" type="button" data-toggle="collapse"
  data-target="#megamenu" aria-expanded="true"><svg class="bi bi-grid" width="20" height="20"
  viewbox="0 0 16 16" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
  <path fill-rule="evenodd" d="M1 2.5A1.5 1.5 0 0 1 2.5 1h3A1.5 1.5 0 0 1 7 2.5v3A1.5 1.5 0 0 1 5.5 7h-3A1.5 1.5 0 0 1 1 5.5v-3zM2.5 2a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zm6.5.5A1.5 1.5 0 0 1 10.5 1h3A1.5 1.5 0 0 1 15 2.5v3A1.5 1.5 0 0 1 13.5 7h-3A1.5 1.5 0 0 1 9 5.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zM1 10.5A1.5 1.5 0 0 1 2.5 9h3A1.5 1.5 0 0 1 7 10.5v3A1.5 1.5 0 0 1 5.5 15h-3A1.5 1.5 0 0 1 1 13.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3zm6.5.5A1.5 1.5 0 0 1 10.5 9h3a1.5 1.5 0 0 1 1.5 1.5v3a1.5 1.5 0 0 1-1.5 1.5h-3A1.5 1.5 0 0 1 9 13.5v-3zm1.5-.5a.5.5 0 0 0-.5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 0-.5-.5h-3z"></path></svg>
  </button>
</nav>

<div id="megamenu" class="megamenu collapse"></div>


      <div class="container-fluid" id="content">
          



<div class="container-download">
  <div id="breadcrumbs">
    <ol class="breadcrumb break-long-content">
      <li class="breadcrumb-item"><a href="/">^</a></li>
      <li class="breadcrumb-item"><a href="/tumbleweed/">tumbleweed</a></li>
      <li class="breadcrumb-item"><a href="/tumbleweed/repo/">repo</a></li>
      <li class="breadcrumb-item"><a href="/tumbleweed/repo/oss/">oss</a></li>
      <li class="breadcrumb-item"><a href="/tumbleweed/repo/oss/repodata/">repodata</a></li>
      <li class="breadcrumb-item active">repomd.xml</li>
    </ol>
  </div>

  <div id="fileinfo">
    <h4>File information</h4>
    <ul>
      <li>Filename: repomd.xml</li>
      <li>Path: /tumbleweed/repo/oss/repodata/repomd.xml</li>
      <li>Size: 14KiB (14018 bytes)</li>
      <li>Last modified: 08-Aug-2025 07:13:40 (Unix timestamp: 1754637220)</li>
      <li><a href="/tumbleweed/repo/oss/repodata/repomd.xml.metalink">Metalink (v3.0)</a></li>
      <li><a href="/tumbleweed/repo/oss/repodata/repomd.xml.meta4">Metalink (v4.0)</a></li>
      <li>Origin: <a href="https://download.opensuse.org/tumbleweed/repo/oss/repodata/repomd.xml">https://download.opensuse.org/tumbleweed/repo/oss/repodata/repomd.xml</a></li>
    </ul>
  </div>

  <h4>Mirrors</h4>
  <p>List of best mirrors for IP address **************, located at 31.2222,121.4581 in (CN)</p>
<h5><a onclick="toggleMap(31.2222,121.4581, 0);event.preventDefault();">Mirrors which handle this country: </a><i id="h51" class="far fa-map" onclick="toggleMap(31.2222,121.4581,0);">9</i></h5>
<div id="map1" style="width: 600px; height: 400px; display: none"></div>
    <ul>
      <li><a href="https://mirrors.tuna.tsinghua.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirrors.tuna.tsinghua.edu.cn</a> (CN)</li>
      <script>
      mirrors_country.push({
        url:"https://mirrors.tuna.tsinghua.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"34.773",
        lng:"113.722",
      });
      </script>
      <li><a href="https://mirrors.ustc.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirrors.ustc.edu.cn</a> (CN)</li>
      <script>
      mirrors_country.push({
        url:"https://mirrors.ustc.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"34.773",
        lng:"113.722",
      });
      </script>
      <li><a href="https://mirror.sjtu.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.sjtu.edu.cn</a> (CN)</li>
      <script>
      mirrors_country.push({
        url:"https://mirror.sjtu.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"34.773",
        lng:"113.722",
      });
      </script>
      <li><a href="https://mirrors.nju.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirrors.nju.edu.cn</a> (CN)</li>
      <script>
      mirrors_country.push({
        url:"https://mirrors.nju.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"34.773",
        lng:"113.722",
      });
      </script>
      <li><a href="https://mirrors.bfsu.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirrors.bfsu.edu.cn</a> (CN)</li>
      <script>
      mirrors_country.push({
        url:"https://mirrors.bfsu.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"39.907",
        lng:"116.397",
      });
      </script>
      <li><a href="https://mirror.bjtu.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.bjtu.edu.cn</a> (CN)</li>
      <script>
      mirrors_country.push({
        url:"https://mirror.bjtu.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"34.773",
        lng:"113.722",
      });
      </script>
      <li><a href="https://mirror.nyist.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.nyist.edu.cn</a> (CN)</li>
      <script>
      mirrors_country.push({
        url:"https://mirror.nyist.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"36.099",
        lng:"114.410",
      });
      </script>
      <li><a href="http://mirrors.zju.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirrors.zju.edu.cn</a> (CN)</li>
      <script>
      mirrors_country.push({
        url:"http://mirrors.zju.edu.cn/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"34.773",
        lng:"113.722",
      });
      </script>
      <li><a href="http://mirrors.163.com/openSUSE/tumbleweed/repo/oss/repodata/repomd.xml">mirrors.163.com</a> (CN)</li>
      <script>
      mirrors_country.push({
        url:"http://mirrors.163.com/openSUSE/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"30.299",
        lng:"120.161",
      });
      </script>
    </ul>



<h5><a onclick="toggleMap(31.2222,121.4581, 1);event.preventDefault();">Mirrors in other countries, but same continent: </a><i id="h52" class="far fa-map" onclick="toggleMap(31.2222,121.4581, 1);">9</i></h5>
<div id="map2" style="width: 600px; height: 400px; display: none"></div>
    <ul id="ul1001">
      <li><a href="http://opensuse.ucom.am/tumbleweed/repo/oss/repodata/repomd.xml">opensuse.ucom.am</a> (AM)</li>
      <script>
      mirrors_region.push({
        url:"http://opensuse.ucom.am/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"40.182",
        lng:"44.510",
      });
      </script>
      <li><a href="https://mr.heru.id/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mr.heru.id</a> (ID)</li>
      <script>
      mirrors_region.push({
        url:"https://mr.heru.id/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"-6.235",
        lng:"106.992",
      });
      </script>
      <li><a href="https://mirror.isoc.org.il/pub/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.isoc.org.il</a> (IL)</li>
      <script>
      mirrors_region.push({
        url:"https://mirror.isoc.org.il/pub/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"31.500",
        lng:"34.750",
      });
      </script>
      <li><a href="https://ftp.riken.jp/Linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.riken.jp</a> (JP)</li>
      <script>
      mirrors_region.push({
        url:"https://ftp.riken.jp/Linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"35.856",
        lng:"139.642",
      });
      </script>
      <li><a href="https://mirror.hashy0917.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.hashy0917.net</a> (JP)</li>
      <script>
      mirrors_region.push({
        url:"https://mirror.hashy0917.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"35.690",
        lng:"139.690",
      });
      </script>
      <li><a href="http://download.nus.edu.sg/mirror/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">download.nus.edu.sg</a> (SG)</li>
      <script>
      mirrors_region.push({
        url:"http://download.nus.edu.sg/mirror/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"1.367",
        lng:"103.801",
      });
      </script>
      <li><a href="http://opensuse-mirror-gce-ap.susecloud.net/tumbleweed/repo/oss/repodata/repomd.xml">opensuse-mirror-gce-ap.susecloud.net</a> (TW)</li>
      <script>
      mirrors_region.push({
        url:"http://opensuse-mirror-gce-ap.susecloud.net/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"25.050",
        lng:"121.532",
      });
      </script>
      <li><a href="https://free.nchc.org.tw/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">free.nchc.org.tw</a> (TW)</li>
      <script>
      mirrors_region.push({
        url:"https://free.nchc.org.tw/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"24.000",
        lng:"121.000",
      });
      </script>
      <li><a href="http://mirror.dc.uz/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.dc.uz</a> (UZ)</li>
      <script>
      mirrors_region.push({
        url:"http://mirror.dc.uz/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"41.667",
        lng:"63.833",
      });
      </script>
    </ul>

<h5><a onclick="toggleMap(31.2222,121.4581, 2 );event.preventDefault();">Mirrors in other parts of the world: </a><i id="h53" class="far fa-map" onclick="toggleMap(31.2222,121.4581,2);">66</i></h5>
<div id="map3" style="width: 600px; height: 400px; display: none"></div>
    <ul id="ul1002">
      <li><a href="https://opensuse.unc.edu.ar/tumbleweed/repo/oss/repodata/repomd.xml">opensuse.unc.edu.ar</a> (AR)</li>
      <script>
      mirrors_rest.push({
        url:"https://opensuse.unc.edu.ar/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"-31.425",
        lng:"-64.175",
      });
      </script>
      <li><a href="https://mirror.easyname.at/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.easyname.at</a> (AT)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.easyname.at/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"48.154",
        lng:"16.386",
      });
      </script>
      <li><a href="http://opensuse-mirror-gce-eu.opensu.se/tumbleweed/repo/oss/repodata/repomd.xml">opensuse-mirror-gce-eu.opensu.se</a> (BE)</li>
      <script>
      mirrors_rest.push({
        url:"http://opensuse-mirror-gce-eu.opensu.se/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"50.853",
        lng:"4.347",
      });
      </script>
      <li><a href="https://opensuse.ipacct.com/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">opensuse.ipacct.com</a> (BG)</li>
      <script>
      mirrors_rest.push({
        url:"https://opensuse.ipacct.com/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"42.695",
        lng:"23.325",
      });
      </script>
      <li><a href="https://download.opensuse.net.br/tumbleweed/repo/oss/repodata/repomd.xml">download.opensuse.net.br</a> (BR)</li>
      <script>
      mirrors_rest.push({
        url:"https://download.opensuse.net.br/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"-22.831",
        lng:"-43.219",
      });
      </script>
      <li><a href="https://linorg.usp.br/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">linorg.usp.br</a> (BR)</li>
      <script>
      mirrors_rest.push({
        url:"https://linorg.usp.br/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"-22.831",
        lng:"-43.219",
      });
      </script>
      <li><a href="https://mirror-br.opensuse.org/tumbleweed/repo/oss/repodata/repomd.xml">mirror-br.opensuse.org</a> (BR)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror-br.opensuse.org/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"-23.534",
        lng:"-46.636",
      });
      </script>
      <li><a href="https://mirror.uepg.br/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.uepg.br</a> (BR)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.uepg.br/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"-22.831",
        lng:"-43.219",
      });
      </script>
      <li><a href="https://opensuse.c3sl.ufpr.br/tumbleweed/repo/oss/repodata/repomd.xml">opensuse.c3sl.ufpr.br</a> (BR)</li>
      <script>
      mirrors_rest.push({
        url:"https://opensuse.c3sl.ufpr.br/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"-25.503",
        lng:"-49.291",
      });
      </script>
      <li><a href="https://mirror.xenyth.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.xenyth.net</a> (CA)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.xenyth.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"43.655",
        lng:"-79.362",
      });
      </script>
      <li><a href="https://opensusemirror.lihaso.com/tumbleweed/repo/oss/repodata/repomd.xml">opensusemirror.lihaso.com</a> (CH)</li>
      <script>
      mirrors_rest.push({
        url:"https://opensusemirror.lihaso.com/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"47.487",
        lng:"9.483",
      });
      </script>
      <li><a href="https://mirror.library.ucy.ac.cy/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.library.ucy.ac.cy</a> (CY)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.library.ucy.ac.cy/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"35.164",
        lng:"33.364",
      });
      </script>
      <li><a href="https://ftp.sh.cvut.cz/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.sh.cvut.cz</a> (CZ)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.sh.cvut.cz/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"50.080",
        lng:"14.505",
      });
      </script>
      <li><a href="https://mirror.karneval.cz/pub/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.karneval.cz</a> (CZ)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.karneval.cz/pub/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"50.088",
        lng:"14.412",
      });
      </script>
      <li><a href="https://ftp.gwdg.de/pub/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.gwdg.de</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.gwdg.de/pub/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"51.513",
        lng:"9.952",
      });
      </script>
      <li><a href="https://ftp.halifax.rwth-aachen.de/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.halifax.rwth-aachen.de</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.halifax.rwth-aachen.de/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"50.748",
        lng:"6.048",
      });
      </script>
      <li><a href="https://ftp.rz.uni-wuerzburg.de/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.rz.uni-wuerzburg.de</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.rz.uni-wuerzburg.de/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"49.590",
        lng:"10.359",
      });
      </script>
      <li><a href="https://ftp.tu-chemnitz.de/pub/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.tu-chemnitz.de</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.tu-chemnitz.de/pub/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"51.299",
        lng:"9.491",
      });
      </script>
      <li><a href="https://ftp.tu-ilmenau.de/mirror/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.tu-ilmenau.de</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.tu-ilmenau.de/mirror/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"51.299",
        lng:"9.491",
      });
      </script>
      <li><a href="https://ftp.uni-bayreuth.de/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.uni-bayreuth.de</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.uni-bayreuth.de/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"49.950",
        lng:"11.598",
      });
      </script>
      <li><a href="https://ftp.uni-erlangen.de/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.uni-erlangen.de</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.uni-erlangen.de/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"49.585",
        lng:"11.009",
      });
      </script>
      <li><a href="https://mirror.de.leaseweb.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.de.leaseweb.net</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.de.leaseweb.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"50.110",
        lng:"8.715",
      });
      </script>
      <li><a href="https://mirror.tele-media.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.tele-media.net</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.tele-media.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"49.183",
        lng:"7.169",
      });
      </script>
      <li><a href="https://mirror1.hs-esslingen.de/pub/Mirrors/ftp.opensuse.org/tumbleweed/repo/oss/repodata/repomd.xml">mirror1.hs-esslingen.de</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror1.hs-esslingen.de/pub/Mirrors/ftp.opensuse.org/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"49.229",
        lng:"9.219",
      });
      </script>
      <li><a href="https://opensuse.0x0.st/tumbleweed/repo/oss/repodata/repomd.xml">opensuse.0x0.st</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://opensuse.0x0.st/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"51.299",
        lng:"9.491",
      });
      </script>
      <li><a href="https://opensuse.schlundtech.de/tumbleweed/repo/oss/repodata/repomd.xml">opensuse.schlundtech.de</a> (DE)</li>
      <script>
      mirrors_rest.push({
        url:"https://opensuse.schlundtech.de/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"51.299",
        lng:"9.491",
      });
      </script>
      <li><a href="https://mirrors.dotsrc.org/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirrors.dotsrc.org</a> (DK)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirrors.dotsrc.org/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"55.637",
        lng:"12.472",
      });
      </script>
      <li><a href="https://ftp.funet.fi/pub/mirrors/ftp.opensuse.com/pub/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.funet.fi</a> (FI)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.funet.fi/pub/mirrors/ftp.opensuse.com/pub/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"60.172",
        lng:"24.935",
      });
      </script>
      <li><a href="https://mirror.aardsoft.fi/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.aardsoft.fi</a> (FI)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.aardsoft.fi/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"60.172",
        lng:"24.935",
      });
      </script>
      <li><a href="https://fr2.rpmfind.net/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">fr2.rpmfind.net</a> (FR)</li>
      <script>
      mirrors_rest.push({
        url:"https://fr2.rpmfind.net/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"48.818",
        lng:"2.317",
      });
      </script>
      <li><a href="https://ftp.cc.uoc.gr/mirrors/linux/opensuse/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.cc.uoc.gr</a> (GR)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.cc.uoc.gr/mirrors/linux/opensuse/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"35.327",
        lng:"25.128",
      });
      </script>
      <li><a href="https://quantum-mirror.hu/mirrors/pub/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">quantum-mirror.hu</a> (HU)</li>
      <script>
      mirrors_rest.push({
        url:"https://quantum-mirror.hu/mirrors/pub/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"47.609",
        lng:"18.509",
      });
      </script>
      <li><a href="https://opensuse.mirror.garr.it/mirrors/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">opensuse.mirror.garr.it</a> (IT)</li>
      <script>
      mirrors_rest.push({
        url:"https://opensuse.mirror.garr.it/mirrors/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"45.398",
        lng:"8.920",
      });
      </script>
      <li><a href="https://opensuse.mirror.liquidtelecom.com/tumbleweed/repo/oss/repodata/repomd.xml">opensuse.mirror.liquidtelecom.com</a> (KE)</li>
      <script>
      mirrors_rest.push({
        url:"https://opensuse.mirror.liquidtelecom.com/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"-1.284",
        lng:"36.816",
      });
      </script>
      <li><a href="https://opensuse.koyanet.lv/tumbleweed/repo/oss/repodata/repomd.xml">opensuse.koyanet.lv</a> (LV)</li>
      <script>
      mirrors_rest.push({
        url:"https://opensuse.koyanet.lv/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"56.999",
        lng:"24.997",
      });
      </script>
      <li><a href="https://repo.fedora.md/mirrors/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">repo.fedora.md</a> (MD)</li>
      <script>
      mirrors_rest.push({
        url:"https://repo.fedora.md/mirrors/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"47.004",
        lng:"28.857",
      });
      </script>
      <li><a href="https://mirror.opensuse.mu/tumbleweed/repo/oss/repodata/repomd.xml">mirror.opensuse.mu</a> (MU)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.opensuse.mu/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"-20.300",
        lng:"57.583",
      });
      </script>
      <li><a href="https://mirror.nl.leaseweb.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.nl.leaseweb.net</a> (NL)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.nl.leaseweb.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"52.382",
        lng:"4.899",
      });
      </script>
      <li><a href="https://opensuse.mirror.liteserver.nl/tumbleweed/repo/oss/repodata/repomd.xml">opensuse.mirror.liteserver.nl</a> (NL)</li>
      <script>
      mirrors_rest.push({
        url:"https://opensuse.mirror.liteserver.nl/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"52.538",
        lng:"5.697",
      });
      </script>
      <li><a href="http://ftp.uninett.no/pub/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.uninett.no</a> (NO)</li>
      <script>
      mirrors_rest.push({
        url:"http://ftp.uninett.no/pub/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"59.955",
        lng:"10.859",
      });
      </script>
      <li><a href="https://mirror.2degrees.nz/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.2degrees.nz</a> (NZ)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.2degrees.nz/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"-42.001",
        lng:"173.998",
      });
      </script>
      <li><a href="http://ftp.icm.edu.pl/pub/Linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.icm.edu.pl</a> (PL)</li>
      <script>
      mirrors_rest.push({
        url:"http://ftp.icm.edu.pl/pub/Linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"52.239",
        lng:"21.036",
      });
      </script>
      <li><a href="https://ftp.man.poznan.pl/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.man.poznan.pl</a> (PL)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.man.poznan.pl/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"52.405",
        lng:"16.934",
      });
      </script>
      <li><a href="https://ftp.psnc.pl/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.psnc.pl</a> (PL)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.psnc.pl/linux/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"52.712",
        lng:"16.378",
      });
      </script>
      <li><a href="https://mirrors.nxthost.com/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirrors.nxthost.com</a> (RO)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirrors.nxthost.com/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"45.997",
        lng:"24.997",
      });
      </script>
      <li><a href="https://mirror.linux-ia64.org/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.linux-ia64.org</a> (RU)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.linux-ia64.org/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"54.902",
        lng:"83.034",
      });
      </script>
      <li><a href="https://mirror.tspu.ru/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.tspu.ru</a> (RU)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.tspu.ru/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"56.491",
        lng:"84.995",
      });
      </script>
      <li><a href="http://ftp.lysator.liu.se/pub/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.lysator.liu.se</a> (SE)</li>
      <script>
      mirrors_rest.push({
        url:"http://ftp.lysator.liu.se/pub/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"58.402",
        lng:"15.646",
      });
      </script>
      <li><a href="https://ftp.acc.umu.se/mirror/opensuse.org/tumbleweed/repo/oss/repodata/repomd.xml">ftp.acc.umu.se</a> (SE)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.acc.umu.se/mirror/opensuse.org/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"59.325",
        lng:"18.056",
      });
      </script>
      <li><a href="https://tux.rainside.sk/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">tux.rainside.sk</a> (SK)</li>
      <script>
      mirrors_rest.push({
        url:"https://tux.rainside.sk/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"48.183",
        lng:"17.038",
      });
      </script>
      <li><a href="https://ftp.linux.org.tr/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">ftp.linux.org.tr</a> (TR)</li>
      <script>
      mirrors_rest.push({
        url:"https://ftp.linux.org.tr/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"41.021",
        lng:"28.995",
      });
      </script>
      <li><a href="https://mirror.verinomi.com/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.verinomi.com</a> (TR)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.verinomi.com/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"41.021",
        lng:"28.995",
      });
      </script>
      <li><a href="https://distrohub.kyiv.ua/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">distrohub.kyiv.ua</a> (UA)</li>
      <script>
      mirrors_rest.push({
        url:"https://distrohub.kyiv.ua/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"50.458",
        lng:"30.530",
      });
      </script>
      <li><a href="http://mirror.siena.edu/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.siena.edu</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"http://mirror.siena.edu/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"42.747",
        lng:"-73.753",
      });
      </script>
      <li><a href="http://mirrors.acm.wpi.edu/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirrors.acm.wpi.edu</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"http://mirrors.acm.wpi.edu/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"42.282",
        lng:"-71.826",
      });
      </script>
      <li><a href="http://opensuse-mirror-gce-us.susecloud.net/tumbleweed/repo/oss/repodata/repomd.xml">opensuse-mirror-gce-us.susecloud.net</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"http://opensuse-mirror-gce-us.susecloud.net/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"41.259",
        lng:"-95.852",
      });
      </script>
      <li><a href="https://codingflyboy.mm.fcix.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">codingflyboy.mm.fcix.net</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"https://codingflyboy.mm.fcix.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"37.751",
        lng:"-97.822",
      });
      </script>
      <li><a href="https://mirror.clarkson.edu/opensuse/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.clarkson.edu</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.clarkson.edu/opensuse/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"44.664",
        lng:"-74.984",
      });
      </script>
      <li><a href="https://mirror.fcix.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.fcix.net</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.fcix.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"37.517",
        lng:"-121.919",
      });
      </script>
      <li><a href="https://mirror.sfo12.us.leaseweb.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.sfo12.us.leaseweb.net</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.sfo12.us.leaseweb.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"37.388",
        lng:"-121.876",
      });
      </script>
      <li><a href="https://mirror.umd.edu/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.umd.edu</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.umd.edu/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"37.751",
        lng:"-97.822",
      });
      </script>
      <li><a href="https://mirror.us.leaseweb.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirror.us.leaseweb.net</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirror.us.leaseweb.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"39.002",
        lng:"-77.096",
      });
      </script>
      <li><a href="https://mirrors.ocf.berkeley.edu/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">mirrors.ocf.berkeley.edu</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"https://mirrors.ocf.berkeley.edu/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"37.868",
        lng:"-122.287",
      });
      </script>
      <li><a href="https://nnenix.mm.fcix.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml">nnenix.mm.fcix.net</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"https://nnenix.mm.fcix.net/opensuse/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"44.823",
        lng:"-68.800",
      });
      </script>
      <li><a href="https://slc-mirror.opensuse.org/tumbleweed/repo/oss/repodata/repomd.xml">slc-mirror.opensuse.org</a> (US)</li>
      <script>
      mirrors_rest.push({
        url:"https://slc-mirror.opensuse.org/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"40.000",
        lng:"-111.000",
      });
      </script>
      <li><a href="https://opensuse.mirror.ac.za/tumbleweed/repo/oss/repodata/repomd.xml">opensuse.mirror.ac.za</a> (ZA)</li>
      <script>
      mirrors_rest.push({
        url:"https://opensuse.mirror.ac.za/tumbleweed/repo/oss/repodata/repomd.xml",
        country:"",
        lat:"-29.000",
        lng:"24.000",
      });
      </script>
    </ul>

<button onclick="toggleMap(31.2222,121.4581,3);">Toggle map</button>
<div id="mapAll" style="width: 600px; height: 400px; display: none"></div>
</div>

      </div>

      <footer class="footer">
  <div class="container">
    <div class="d-flex justify-content-between">
      <div class="footer-copyright">
        &copy; 2021-2025 SUSE LLC., openSUSE contributors
      </div>
      <div class="list-inline">
        <a class="list-inline-item" href="https://en.opensuse.org/Imprint">Legal notice</a>
        <a class="list-inline-item" href="https://github.com/openSUSE/MirrorCache">Source code</a>
        <a class="list-inline-item" href="https://github.com/openSUSE/MirrorCache/issues/new">Report issue</a>
        <a>MirrorCache 1.096
</a>
      </div>
    </div>
  </div>
</footer>

  </body>
</html>

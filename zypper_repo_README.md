# zypper_repo.sh - openSUSE Tumbleweed 镜像源自动配置脚本

## 功能描述

这个脚本的目的是从 openSUSE Tumbleweed 中国、日本的几个镜像站点中按照 ping 延迟排序尝试找到一个工作的镜像并设置 zypper，如果所有镜像都不可用则使用官方站点。

## 支持的镜像站

脚本会按照 ping 延迟从低到高的顺序测试以下镜像站：

### 中国镜像站
- 清华大学 TUNA 镜像站: `https://mirrors.tuna.tsinghua.edu.cn/opensuse/tumbleweed/repo/`
- 中科大镜像站: `https://mirrors.ustc.edu.cn/opensuse/tumbleweed/repo/`
- 上海交通大学镜像站: `https://ftp.sjtu.edu.cn/opensuse/tumbleweed/repo/`
- 南京大学镜像站: `https://mirrors.nju.edu.cn/opensuse/tumbleweed/repo/`

### 日本镜像站
- RIKEN 镜像站: `https://ftp.riken.jp/Linux/opensuse/tumbleweed/repo/`
- OSDN 镜像站: `https://mirrors.dl.osdn.jp/opensuse-tumbleweed/repo/`

### 美国镜像站
- Kernel.org 镜像站: `https://mirrors.kernel.org/opensuse/tumbleweed/repo/`
- FCIX 镜像站: `https://mirror.fcix.net/opensuse/tumbleweed/repo/`
- UC Berkeley 镜像站: `https://mirrors.ocf.berkeley.edu/opensuse/tumbleweed/repo/`

### 官方源（备用）
- openSUSE 官方源: `https://download.opensuse.org/tumbleweed/repo/`

## 使用方法

### 正常使用
```bash
sudo ./zypper_repo.sh
```

### 测试模式（不实际修改 zypper 配置）
```bash
TEST_MODE=1 ./zypper_repo.sh
```

## 工作流程

1. **延迟测试**: 对所有镜像站进行 ping 测试，测量网络延迟（每个镜像2秒超时）
2. **排序**: 按照延迟从低到高排序镜像站
3. **可用性检查**: 按顺序检查每个镜像站的可用性（通过检查 `oss/repodata/repomd.xml` 和 `non-oss/repodata/repomd.xml` 文件，每个检查2秒超时）
4. **配置更新**: 找到第一个可用的镜像站后，更新 zypper 配置
5. **错误处理**: 如果配置失败，继续尝试下一个镜像站
6. **备用方案**: 如果所有镜像站都不可用，使用官方源

## 配置的仓库

脚本会配置以下两个仓库：
- `repo-oss`: OSS (Open Source Software) 仓库
- `repo-non-oss`: Non-OSS 仓库

注意：Tumbleweed 是滚动发布版本，通常不需要单独的 update 仓库。

## 注意事项

1. 脚本需要 sudo 权限来修改 zypper 配置
2. 脚本会自动移除现有的同名仓库，然后添加新的仓库
3. 配置完成后会自动刷新仓库缓存
4. 建议在测试模式下先运行一次，确认选择的镜像站合适

## 依赖工具

- `curl`: 用于检查镜像站可用性
- `ping`: 用于测试网络延迟
- `zypper`: openSUSE 包管理器
- `sudo`: 用于获取管理员权限

## 故障排除

如果脚本运行失败，可能的原因：
1. 网络连接问题
2. 镜像站临时不可用
3. zypper 配置权限问题

可以使用测试模式查看脚本的选择逻辑：
```bash
TEST_MODE=1 ./zypper_repo.sh
```
